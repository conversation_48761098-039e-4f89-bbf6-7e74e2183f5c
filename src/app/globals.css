@import "tailwindcss";

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
  --golf-green: #2d5016;
  --golf-gold: #d4af37;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

.golf-green {
  color: var(--golf-green);
}

.bg-golf-green {
  background-color: var(--golf-green);
}

.golf-gold {
  color: var(--golf-gold);
}

.bg-golf-gold {
  background-color: var(--golf-gold);
}

.hero-gradient {
  background: linear-gradient(135deg, var(--golf-green) 0%, #4a7c59 100%);
}
